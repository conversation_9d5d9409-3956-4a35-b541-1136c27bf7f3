<template>
  <view class="packages-container">
    <!-- 顶部标签 -->
    <view class="tabs-container">
      <view class="tab-item" :class="{ active: activeTab === 'packages' }" @click="switchTab('packages')">
        套餐
      </view>
      <view class="tab-item" :class="{ active: activeTab === 'invite' }" @click="switchTab('invite')">
        转增
      </view>
    </view>

    <!-- 套餐列表 -->
    <view v-if="activeTab === 'packages'" class="packages-list">
      <view
        v-for="pkg in packages"
        :key="pkg.id"
        class="package-item"
        :class="{ selected: selectedPackage && selectedPackage.id === pkg.id }"
        @click="selectPackage(pkg)"
      >
        <view class="package-header">
          <text class="package-title">{{ pkg.title }}</text>
          <view class="package-price">
            <text class="price-value">{{ pkg.price }}</text>
            <text class="price-unit">元</text>
          </view>
        </view>
        <view class="package-desc">{{ pkg.description || '测试' }}</view>
        <view class="package-credits">发布条数 ({{ pkg.credits_amount }}条)</view>

        <!-- 选中状态指示器 -->
        <view v-if="selectedPackage && selectedPackage.id === pkg.id" class="selected-indicator">
          <text class="check-icon">✓</text>
        </view>
      </view>

      <!-- 免费获取发布条数 -->
      <view class="free-section">
        <text class="free-title">免费获取发布条数</text>
        <text class="free-desc">* 每邀请1人 新增1条发布条数</text>
        <button class="share-btn" @click="goToInvite">去分享</button>
      </view>
    </view>

    <!-- 邀请转增 -->
    <view v-if="activeTab === 'invite'" class="invite-section">
      <view class="invite-info">
        <text class="invite-title">邀请好友获得发布条数</text>
        <text class="invite-desc">每成功邀请1位好友注册，您将获得1条发布条数</text>
      </view>
      
      <view class="invite-stats">
        <view class="stat-item">
          <text class="stat-number">{{ inviteStats.totalInvites || 0 }}</text>
          <text class="stat-label">累计邀请</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ inviteStats.successInvites || 0 }}</text>
          <text class="stat-label">成功邀请</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ inviteStats.earnedCredits || 0 }}</text>
          <text class="stat-label">获得条数</text>
        </view>
      </view>

      <button class="invite-btn" @click="shareInvite">立即邀请</button>
    </view>

    <!-- 底部信息 -->
    <view class="bottom-info">
      <view class="user-credits">
        <text class="credits-label">小计</text>
        <text class="credits-value">¥{{ totalAmount }}</text>
      </view>
      
      <view class="payment-method">
        <text class="payment-label">支付方式</text>
        <view class="payment-option">
          <image src="/static/wechat-pay.png" class="payment-icon" mode="aspectFit" />
          <text class="payment-text">微信支付</text>
          <view class="payment-check">✓</view>
        </view>
      </view>

      <button v-if="activeTab === 'packages'" class="pay-btn" @click="handlePay" :disabled="!selectedPackage">
        立即领取
      </button>
      
      <button class="history-btn" @click="goToHistory">我的购买记录</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { packageAPI, userAPI, utils } from '@/utils/api.js'

// 响应式数据
const activeTab = ref('packages')
const packages = ref([])
const selectedPackage = ref(null)
const inviteStats = ref({})
const isLoading = ref(false)

// 计算属性
const totalAmount = computed(() => {
  return selectedPackage.value ? selectedPackage.value.price : '0.00'
})

// 生命周期
onMounted(() => {
  loadPackages()
  loadInviteStats()
})

// 方法定义
const switchTab = (tab) => {
  activeTab.value = tab
}

const loadPackages = async () => {
  try {
    isLoading.value = true
    const response = await packageAPI.getPackages()
    
    if (response.success) {
      packages.value = response.data.items || []
      // 默认选择第一个套餐
      if (packages.value.length > 0) {
        selectedPackage.value = packages.value[0]
      }
    } else {
      utils.handleError(new Error(response.message), '获取套餐失败')
    }
  } catch (error) {
    utils.handleError(error, '获取套餐失败')
  } finally {
    isLoading.value = false
  }
}

const loadInviteStats = async () => {
  try {
    const response = await userAPI.getInviteStats()
    if (response.success) {
      inviteStats.value = response.data
    }
  } catch (error) {
    console.error('获取邀请统计失败:', error)
  }
}

const selectPackage = (pkg) => {
  selectedPackage.value = pkg
  console.log('选择套餐:', pkg.title, '价格:', pkg.price)
}

const handlePay = async () => {
  if (!selectedPackage.value) {
    uni.showToast({
      title: '请选择套餐',
      icon: 'none'
    })
    return
  }

  try {
    // 模拟支付流程
    uni.showLoading({ title: '支付中...' })
    
    const response = await packageAPI.createOrder({
      package_id: selectedPackage.value.id
    })
    
    if (response.success) {
      // 模拟支付成功
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '支付成功',
          icon: 'success'
        })
        
        // 跳转到聊天页面
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/chat/chat'
          })
        }, 1500)
      }, 2000)
    } else {
      uni.hideLoading()
      utils.handleError(new Error(response.message), '支付失败')
    }
  } catch (error) {
    uni.hideLoading()
    utils.handleError(error, '支付失败')
  }
}

const goToInvite = () => {
  activeTab.value = 'invite'
}

const shareInvite = () => {
  uni.navigateTo({
    url: '/pages/invite/invite'
  })
}

const goToHistory = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}
</script>

<style scoped>
.packages-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.tabs-container {
  display: flex;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 32rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  color: #22c55e;
  border-bottom-color: #22c55e;
  font-weight: bold;
}

.packages-list {
  padding: 20rpx;
}

.package-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.package-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

.package-item.selected {
  border: 2rpx solid #22c55e;
  background: #f0fdf4;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.package-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.package-price {
  display: flex;
  align-items: baseline;
}

.price-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b35;
}

.price-unit {
  font-size: 24rpx;
  color: #ff6b35;
  margin-left: 5rpx;
}

.package-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.package-credits {
  font-size: 28rpx;
  color: #22c55e;
  font-weight: bold;
}

.selected-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background: #22c55e;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.free-section {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  text-align: center;
  margin-top: 20rpx;
}

.free-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.free-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.share-btn {
  width: 200rpx;
  height: 70rpx;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
}

.invite-section {
  padding: 40rpx;
}

.invite-info {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.invite-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.invite-desc {
  font-size: 28rpx;
  color: #666;
}

.invite-stats {
  display: flex;
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #22c55e;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.invite-btn {
  width: 100%;
  height: 90rpx;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.bottom-info {
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.user-credits {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.credits-label {
  font-size: 28rpx;
  color: #333;
}

.credits-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.payment-method {
  margin-bottom: 30rpx;
}

.payment-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
}

.payment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.payment-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.payment-check {
  width: 40rpx;
  height: 40rpx;
  background: #22c55e;
  color: white;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.pay-btn {
  width: 100%;
  height: 90rpx;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.pay-btn:disabled {
  background: #ccc;
}

.history-btn {
  width: 100%;
  height: 70rpx;
  background: transparent;
  color: #999;
  border: none;
  font-size: 28rpx;
}
</style>
