const express = require('express');
const router = express.Router();

const OrderPrisma = require('../models/OrderPrisma');
const PackagePrisma = require('../models/PackagePrisma');
const UserPrisma = require('../models/UserPrisma');
const { authenticateToken } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');

/**
 * 接口 9: 创建订单
 * POST /api/v1/orders
 * 
 * 输入: package_id
 * 逻辑: 创建一条 orders 记录，状态为 pending。集成微信支付 SDK，调用统一下单接口，生成支付参数返回给小程序
 * 目前模拟成功
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { package_id } = req.body;
    const userId = req.user.id;

    // 验证必填参数
    if (!package_id) {
      return ResponseHelper.validationError(res, ['package_id 是必填参数']);
    }

    // 验证套餐是否存在且上架
    const packageData = await PackagePrisma.findById(package_id);
    if (!packageData) {
      return ResponseHelper.notFound(res, '套餐不存在');
    }

    if (!packageData.is_active) {
      return ResponseHelper.error(res, '套餐已下架', 400);
    }

    // 验证用户是否存在
    const user = await UserPrisma.findById(userId);
    if (!user) {
      return ResponseHelper.unauthorized(res, '用户不存在');
    }

    // 创建订单
    const orderData = {
      user_id: userId,
      package_id: package_id,
      amount: packageData.price,
      status: 'pending',
      payment_method: 'wechat'
    };

    const order = await OrderPrisma.create(orderData);

    // 模拟微信支付参数（实际应该调用微信支付API）
    const mockPaymentParams = {
      timeStamp: Math.floor(Date.now() / 1000).toString(),
      nonceStr: Math.random().toString(36).substr(2, 15),
      package: `prepay_id=mock_prepay_id_${order.id}`,
      signType: 'RSA',
      paySign: 'mock_pay_sign_' + Math.random().toString(36).substr(2, 10)
    };

    ResponseHelper.success(res, {
      order: order.toJSON(),
      payment_params: mockPaymentParams
    }, '订单创建成功');

  } catch (error) {
    console.error('创建订单错误:', error);
    ResponseHelper.serverError(res, '创建订单失败', error);
  }
});

/**
 * 获取用户订单列表
 * GET /api/v1/orders
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    const result = await OrderPrisma.findByUserId(userId, {
      page: parseInt(page),
      limit: parseInt(limit),
      include: { package: true }
    });

    ResponseHelper.success(res, {
      orders: result.orders.map(order => order.toJSON()),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: result.total,
        totalPages: Math.ceil(result.total / parseInt(limit))
      }
    }, '获取订单列表成功');

  } catch (error) {
    console.error('获取订单列表错误:', error);
    ResponseHelper.serverError(res, '获取订单列表失败', error);
  }
});

/**
 * 根据ID获取订单详情
 * GET /api/v1/orders/:id
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const order = await OrderPrisma.findById(id, {
      include: { user: true, package: true }
    });

    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 验证订单是否属于当前用户
    if (order.user_id.toString() !== userId.toString()) {
      return ResponseHelper.forbidden(res, '无权访问此订单');
    }

    ResponseHelper.success(res, {
      order: order.toJSON()
    }, '获取订单详情成功');

  } catch (error) {
    console.error('获取订单详情错误:', error);
    ResponseHelper.serverError(res, '获取订单详情失败', error);
  }
});

/**
 * 模拟支付接口
 * POST /api/v1/orders/:id/mock-payment
 */
router.post('/:id/mock-payment', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 获取订单
    const order = await OrderPrisma.findById(id, {
      include: { user: true, package: true }
    });

    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 验证订单是否属于当前用户
    if (order.user_id.toString() !== userId.toString()) {
      return ResponseHelper.forbidden(res, '无权访问此订单');
    }

    // 检查订单状态
    if (order.status !== 'pending') {
      return ResponseHelper.error(res, '订单状态不允许支付', 400);
    }

    // 模拟支付成功，更新订单状态
    const updatedOrder = await OrderPrisma.updateStatus(id, 'completed');

    // 更新用户发布条数
    const user = await UserPrisma.findById(userId);
    const newCredits = (user.publishing_credits || 0) + order.package.credits_amount;
    await UserPrisma.updateCredits(userId, newCredits);

    ResponseHelper.success(res, {
      order: updatedOrder.toJSON(),
      message: '支付成功'
    }, '支付成功');

  } catch (error) {
    console.error('模拟支付错误:', error);
    ResponseHelper.serverError(res, '支付失败', error);
  }
});

module.exports = router;
